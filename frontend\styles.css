:root {
    --primary-color: #30abe8;
    --primary-light: #1E546A;
    --secondary-color: #ffc619;
    --text-color: #95D7E3;
    --background-color: #05202e;
    --card-background: #102735;
    --border-color: #30abe8;
    --hover-color: #2a7dc9;
    --active-color: #47ebeb;
    --card-radius: 6px;
    --base-font-size: 12px;
    --small-font-size: 11px;
    --tiny-font-size: 10px;
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
    font-family: 'Microsoft YaHei', '微软雅黑', Arial, sans-serif;
    font-size: var(--base-font-size);
}

body {
    background-color: var(--background-color);
    color: var(--text-color);
    line-height: 1.4;
}

.container {
    max-width: 98%;
    margin: 60px auto;
    padding: 0 10px;
}

header {
    text-align: center;
    margin-bottom: 15px;
}

header h1 {
    font-size: 20px;
    color: var(--primary-color);
    font-weight: 600;
    position: relative;
    display: inline-block;
    padding-bottom: 5px;
}

header h1:after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 50%;
    transform: translateX(-50%);
    width: 60px;
    height: 2px;
    background: var(--primary-color);
    border-radius: 2px;
}

.card {
    background: var(--card-background);
    border-radius: var(--card-radius);
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.3);
    padding: 12px 15px;
    margin-bottom: 12px;
    transition: transform 0.2s ease, box-shadow 0.2s ease;
    border: 1px solid rgba(48, 171, 232, 0.2);
}

.card:hover {
    transform: translateY(-3px);
    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.35);
    border-color: rgba(48, 171, 232, 0.4);
}

h2 {
    color: var(--primary-color);
    font-size: 12px;
    margin-bottom: 10px;
    padding-bottom: 5px;
    border-bottom: 1px solid rgba(48, 171, 232, 0.6);
}

.option-group {
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    gap: 10px;
    margin-bottom: 8px;
    background-color: rgba(5, 32, 46, 0.6);
    border-radius: 4px;
    padding: 8px;
}

.option-group.compact {
    gap: 6px;
    margin-bottom: 5px;
}

.option {
    display: flex;
    align-items: center;
    gap: 4px;
}

.option input[type="radio"] {
    appearance: none;
    width: 12px;
    height: 12px;
    border: 1px solid var(--border-color);
    border-radius: 50%;
    outline: none;
    cursor: pointer;
    position: relative;
}

.option input[type="radio"]:checked {
    border-color: var(--primary-color);
}

.option input[type="radio"]:checked::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 6px;
    height: 6px;
    background-color: var(--primary-color);
    border-radius: 50%;
}

.option label {
    font-size: 11px;
    cursor: pointer;
    color: var(--text-color);
}

.upload-btn {
    background-color: transparent;
    color: var(--primary-color);
    border: 1px solid var(--primary-color);
    border-radius: 3px;
    padding: 4px 6px;
    font-size: 9px;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 5px;
    transition: all 0.3s;
    margin-left: auto;
    white-space: nowrap;
    min-width: 120px;
    justify-content: center;
}

.upload-btn:hover {
    background-color: rgba(48, 171, 232, 0.3);
    border-color: var(--hover-color);
    transform: translateY(-2px);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.upload-btn:active {
    transform: translateY(0);
    background-color: rgba(48, 171, 232, 0.5);
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.15);
}

/* 加载已有结果按钮样式 */
.load-btn {
    background-color: transparent;
    color: var(--primary-color);
    border: 1px solid var(--primary-color);
    border-radius: 3px;
    padding: 4px 10px;
    font-size: 11px;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 5px;
    transition: all 0.3s;
    white-space: nowrap;
    justify-content: center;
    margin: 0 5px;
}

.load-btn:hover {
    background-color: rgba(48, 171, 232, 0.3);
    border-color: var(--hover-color);
    color: var(--active-color);
}

.sub-options {
    margin-top: 8px;
    background-color: rgba(5, 32, 46, 0.6);
    border-radius: 4px;
    padding: 8px;
}

/* 添加车辆类型和组织时段的水平布局容器 */
.vehicle-time-container {
    display: flex;
    justify-content: space-between;
    flex-wrap: nowrap;
    gap: 10px;
}

.vehicle-time-container .sub-option {
    flex: 1;
    margin-bottom: 5px;
}

.vehicle-time-container .select-wrapper {
    flex: 1;
}

.vehicle-time-container select {
    min-width: 100px;
    width: 100%;
}

.sub-option {
    display: flex;
    align-items: center;
    flex-wrap: wrap;
    margin-bottom: 8px;
    gap: 6px;
}

.sub-option.indent {
    margin-left: 15px;
}

.bullet {
    font-size: 16px;
    color: var(--primary-color);
    line-height: 1;
}

.select-wrapper {
    position: relative;
    margin-right: 8px;
}

.select-wrapper:after {
    content: '▼';
    font-size: 8px;
    color: var(--primary-color);
    position: absolute;
    right: 6px;
    top: 50%;
    transform: translateY(-50%);
    pointer-events: none;
}

select {
    appearance: none;
    padding: 4px 20px 4px 6px;
    border-radius: 3px;
    border: 1px solid var(--primary-color);
    background: var(--primary-light);
    color: var(--text-color);
    font-size: 11px;
    cursor: pointer;
    outline: none;
    min-width: 120px; /* 减小默认最小宽度 */
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
    transition: all 0.2s ease;
}

select:hover {
    border-color: var(--hover-color);
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.25);
}

select:focus {
    border-color: var(--active-color);
    box-shadow: 0 0 0 2px rgba(71, 235, 235, 0.25);
}

select option {
    background-color: var(--background-color);
}

select option:hover {
    background: var(--secondary-color);
}

select:disabled,
input:disabled {
    background-color: rgba(5, 32, 46, 0.8);
    border-color: #4a5a65;
    color: #8b949e;
    cursor: not-allowed;
}

.option-group:has(input[type="radio"]:disabled) {
    opacity: 0.7;
}

/* 改进复选框样式 */
input[type="checkbox"] {
    appearance: none;
    width: 12px;
    height: 12px;
    border: 1px solid var(--border-color);
    border-radius: 3px;
    outline: none;
    cursor: pointer;
    position: relative;
    margin-right: 6px;
    transition: all 0.2s ease;
}

input[type="checkbox"]:hover {
    border-color: var(--hover-color);
    box-shadow: 0 0 0 1px rgba(48, 171, 232, 0.3);
}

input[type="checkbox"]:checked {
    background-color: transparent;
    border-color: var(--active-color);
}

input[type="checkbox"]:checked::before {
    content: '✓';
    position: absolute;
    color: var(--active-color);
    font-size: 10px;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
}

.note {
    color: #8b949e;
    font-size: 11px;
    font-style: italic;
}

.run-btn {
    display: block;
    margin: 15px auto;
    background-color: transparent;
    color: var(--secondary-color);
    border: 1px solid var(--secondary-color);
    border-radius: 3px;
    padding: 8px 20px;
    font-size: 13px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.run-btn:hover {
    background-color: rgba(255, 198, 25, 0.2);
    transform: translateY(-2px);
    box-shadow: 0 3px 6px rgba(0, 0, 0, 0.25);
}

.run-btn:active {
    background-color: rgba(255, 198, 25, 0.3);
    transform: translateY(1px);
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.15);
}

/* 添加结果卡片样式 */
.result-card {
    background: var(--card-background);
    border-radius: var(--card-radius);
    box-shadow: 0 1px 5px rgba(0, 0, 0, 0.2);
    padding: 12px 15px;
    margin-bottom: 12px;
    transition: transform 0.2s ease, box-shadow 0.2s ease;
    color: var(--text-color);
    border: 1px solid rgba(48, 171, 232, 0.3);
}

.result-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 3px 8px rgba(0, 0, 0, 0.25);
    border-color: rgba(48, 171, 232, 0.6);
}

.result-header {
    margin-bottom: 12px;
    padding-bottom: 8px;
    border-bottom: 1px solid rgba(48, 171, 232, 0.6);
}

.result-header h2 {
    color: var(--active-color);
    font-size: 12px;
    margin-bottom: 0;
    padding-bottom: 0;
    border-bottom: none;
}

.metrics-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 10px;
    margin-bottom: 15px;
}

.metric-item {
    text-align: center;
    padding: 8px;
    background-color: var(--primary-light);
    border-radius: 4px;
    border: 1px solid rgba(48, 171, 232, 0.3);
}

.metric-value {
    font-size: 12px;
    font-weight: bold;
    color: var(--secondary-color);
    margin-bottom: 2px;
}

.metric-label {
    font-size: 9px;
    color: var(--text-color);
}

/* 优化图表容器样式 */
.chart-container {
    height: 220px;
    min-height: 180px;
    width: 100%;
    margin-top: 6px;
    background-color: rgba(5, 32, 46, 0.6);
    border-radius: 4px;
    padding: 8px;
    position: relative;
    border: 1px solid rgba(255, 255, 255, 0.1);
    transition: all 0.3s ease;
    box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.2);
}

/* 调整结果卡片样式适应网格布局 */
.result-card {
    background-color: rgba(25, 35, 50, 0.8);
    border-radius: 6px;
    padding: 8px;
    margin-bottom: 6px;
    border: 1px solid rgba(255, 255, 255, 0.05);
    box-shadow: 0 3px 10px rgba(0, 0, 0, 0.25);
    transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.result-card:hover {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.4);
}

.result-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    margin-bottom: 6px;
    padding-bottom: 3px;
}

.result-header h2 {
    margin: 0;
    color: #8acdff;
    font-weight: 500;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

.back-btn {
    background-color: transparent;
    color: var(--primary-color);
    border: 1px solid var(--primary-color);
    border-radius: 4px;
    padding: 8px 15px;
    font-size: 14px;
    cursor: pointer;
    margin-top: 15px;
    align-self: flex-end;
    transition: all 0.3s;
}

.back-btn:hover {
    background-color: rgba(48, 171, 232, 0.3);
    color: var(--active-color);
    border-color: var(--active-color);
}

/* 组织方案网格布局 */
.org-grid {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    grid-gap: 8px;
}

.org-grid .sub-option {
    margin-bottom: 5px;
    font-size: 0.9em;
}

/* 调整选择框大小 */
.org-grid select {
    min-width: 100px;
    padding: 3px 18px 3px 5px;
}

/* 调整信号配时部分 */
.org-grid .option-group.compact {
    padding: 5px;
    margin-bottom: 3px;
}

/* 调整信号配时的布局，使其占据两列 */
.org-grid .sub-option:nth-child(4) {
    grid-column: span 2;
}

/* 缩小复选框间距 */
.checkbox-wrapper {
    margin-left: 5px;
}

/* 禁用状态样式 */
.disabled {
    opacity: 0.6;
    cursor: not-allowed;
}

.disabled select,
.disabled input,
.disabled button {
    pointer-events: none;
}

/* 美化滚动条样式 */
::-webkit-scrollbar {
    width: 8px;
    height: 8px;
}

::-webkit-scrollbar-track {
    background: rgba(0, 0, 0, 0.1);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb {
    background: rgba(255, 255, 255, 0.2);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: rgba(255, 255, 255, 0.3);
}

/* 改进表单元素样式 */
.form-group {
    margin-bottom: 15px;
}

.form-group label {
    display: block;
    margin-bottom: 5px;
    font-weight: bold;
    color: var(--text-color);
}

.form-group input, .form-group textarea {
    width: 100%;
    padding: 8px 12px;
    border: 1px solid var(--primary-color);
    border-radius: 4px;
    background-color: rgba(5, 32, 46, 0.6);
    color: var(--text-color);
    font-size: 12px;
    resize: vertical;
}

.form-group input:focus, .form-group textarea:focus {
    outline: none;
    border-color: var(--active-color);
    box-shadow: 0 0 0 2px rgba(71, 235, 235, 0.2);
}

.config-preview {
    background-color: rgba(30, 84, 106, 0.6);
    border-radius: 6px;
    padding: 15px;
    margin-bottom: 20px;
}

.config-preview h4 {
    margin: 0 0 10px 0;
    color: var(--text-color);
    font-size: 14px;
}

.config-summary {
    font-size: 12px;
    color: #b0c4d1;
    line-height: 1.4;
}

.config-summary div {
    margin-bottom: 5px;
}

/* 模态框操作按钮 */
.modal-actions {
    display: flex;
    gap: 10px;
    justify-content: flex-end;
}

.confirm-btn, .cancel-btn {
    padding: 8px 20px;
    border: none;
    border-radius: 4px;
    font-size: 14px;
    cursor: pointer;
    transition: all 0.3s;
}

.confirm-btn {
    background-color: transparent;
    color: var(--secondary-color);
    border: 1px solid var(--secondary-color);
}

.confirm-btn:hover {
    background-color: rgba(255, 198, 25, 0.2);
}

.cancel-btn {
    background-color: transparent;
    color: var(--primary-color);
    border: 1px solid var(--primary-color);
}

.cancel-btn:hover {
    background-color: rgba(48, 171, 232, 0.3);
}

/* 操作按钮容器样式 */
.action-buttons {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 10px;
    margin: 15px 0;
    flex-wrap: wrap;
}

/* 加载和空状态消息 */
.loading-message, .empty-message {
    text-align: center;
    color: #8b949e;
    font-style: italic;
    padding: 40px 20px;
}

/* 响应式设计 */
@media (max-width: 767px) {
    main {
        flex-direction: column !important;
    }
    
    .metrics-grid {
        grid-template-columns: repeat(2, 1fr);
    }
}

/* 使用网格布局使页面更紧凑 */
@media (min-width: 768px) {
    main {
        display: flex;
        flex-wrap: wrap;
    }
    #organizationSection {
        height: auto;
    }
    .run-btn {
        width: 200px;
        font-size: 14px;
    }
}

@media (max-width: 767px) {
    .container {
        padding: 0 10px;
        margin: 10px auto;
    }
    .option-group {
        flex-direction: column;
        align-items: flex-start;
    }
    .upload-btn {
        margin-left: 0;
        margin-top: 5px;
        min-width: auto;
        width: 100%;
    }
    .sub-option {
        flex-direction: column;
        align-items: flex-start;
    }
    select {
        width: 100%;
    }
    .org-grid {
        grid-template-columns: 1fr;
    }
}

/* 历史方案相关样式 */
.history-btn {
    background-color: transparent;
    color: var(--primary-color);
    border: 1px solid var(--primary-color);
    border-radius: 3px;
    padding: 4px 10px;
    font-size: 11px;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 5px;
    transition: all 0.3s;
    white-space: nowrap;
    justify-content: center;
    margin: 0 5px;
}

.history-btn:hover {
    background-color: rgba(48, 171, 232, 0.3);
    border-color: var(--hover-color);
    color: var(--active-color);
}

/* 模态框样式 */
.modal {
    display: none;
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
}

.modal-content {
    background-color: rgba(5, 32, 46, 0.9);
    margin: 5% auto;
    padding: 0;
    border-radius: 8px;
    width: 80%;
    max-width: 800px;
    max-height: 80vh;
    overflow: hidden;
    box-shadow: 0 6px 25px rgba(0, 0, 0, 0.35), 0 0 1px rgba(48, 171, 232, 0.6);
    border: 1px solid rgba(48, 171, 232, 0.6);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.modal-header {
    background-color: rgba(5, 29, 46, 0.8);
    color: var(--text-color);
    padding: 15px 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-bottom: 1px solid rgba(48, 171, 232, 0.6);
}

.modal-header h3 {
    margin: 0;
    font-size: 9px;
    color: var(--primary-color);
}

.close {
    color: var(--text-color);
    font-size: 24px;
    font-weight: bold;
    cursor: pointer;
    line-height: 1;
}

.close:hover {
    color: var(--active-color);
}

.modal-body {
    padding: 20px;
    color: var(--text-color);
    max-height: 60vh;
    overflow-y: auto;
}

/* 历史方案控制按钮 */
.history-controls {
    display: flex;
    gap: 10px;
    margin-bottom: 20px;
    flex-wrap: wrap;
}

.save-btn, .refresh-btn {
    background-color: transparent;
    color: var(--primary-color);
    border: 1px solid var(--primary-color);
    border-radius: 4px;
    padding: 8px 16px;
    font-size: 12px;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 6px;
    transition: all 0.3s;
}

.save-btn:hover, .refresh-btn:hover {
    background-color: rgba(48, 171, 232, 0.3);
    color: var(--active-color);
    border-color: var(--active-color);
}

/* 历史方案列表 */
.history-list {
    max-height: 400px;
    overflow-y: auto;
}

.history-item {
    background-color: rgba(30, 84, 106, 0.6);
    border-radius: 6px;
    padding: 15px;
    margin-bottom: 10px;
    border-left: 4px solid var(--primary-color);
    transition: all 0.3s;
}

.history-item:hover {
    background-color: rgba(30, 84, 106, 0.8);
    border-left-color: var(--active-color);
}

.history-item-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 8px;
}

.history-item-title {
    font-weight: bold;
    font-size: 14px;
    color: var(--text-color);
}

.history-item-time {
    font-size: 11px;
    color: #8b949e;
}

.history-item-description {
    color: #b0c4d1;
    font-size: 12px;
    margin-bottom: 10px;
    line-height: 1.4;
}

.history-item-config {
    font-size: 11px;
    color: #8b949e;
    margin-bottom: 10px;
}

.history-item-actions {
    display: flex;
    gap: 8px;
    flex-wrap: wrap;
}

.load-scheme-btn, .delete-scheme-btn {
    padding: 4px 12px;
    font-size: 12px;
    border: none;
    border-radius: 3px;
    cursor: pointer;
    transition: all 0.3s;
}

.load-scheme-btn {
    background-color: transparent;
    color: var(--secondary-color);
    border: 1px solid var(--secondary-color);
}

.load-scheme-btn:hover {
    background-color: rgba(255, 198, 25, 0.2);
}

.delete-scheme-btn {
    background-color: transparent;
    color: #ed6f6f;
    border: 1px solid #ed6f6f;
}

.delete-scheme-btn:hover {
    background-color: rgba(237, 111, 111, 0.2);
}

/* 添加动画效果 */
@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes pulse {
    0% {
        box-shadow: 0 0 0 0 rgba(71, 235, 235, 0.4);
    }
    70% {
        box-shadow: 0 0 0 6px rgba(71, 235, 235, 0);
    }
    100% {
        box-shadow: 0 0 0 0 rgba(71, 235, 235, 0);
    }
}

.card {
    animation: fadeIn 0.3s ease-out;
}

.run-btn:focus {
    animation: pulse 1.5s infinite;
}

/* 加载动画效果 */
.loading-container {
    display: flex;
    justify-content: center;
    align-items: center;
    flex-direction: column;
    height: 100%;
    padding: 30px;
}

/* 加载中动画样式 */
.loading-spinner {
    width: 40px;
    height: 40px;
    border: 4px solid rgba(255, 255, 255, 0.2);
    border-left-color: #ffffff;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto 10px;
}

@keyframes spin {
    to {
        transform: rotate(360deg);
    }
}

/* 响应式设计 */
@media (max-width: 767px) {
    .modal-content {
        width: 95%;
        margin: 10% auto;
    }

    .history-controls {
        flex-direction: column;
    }

    .history-item-header {
        flex-direction: column;
        align-items: flex-start;
    }

    .history-item-actions {
        justify-content: flex-start;
    }

    .modal-actions {
        flex-direction: column;
    }

    .confirm-btn, .cancel-btn {
        width: 100%;
    }
}

/* 添加标签组件样式 */
.tag {
    display: inline-flex;
    align-items: center;
    padding: 2px 8px;
    border-radius: 12px;
    font-size: 11px;
    font-weight: 500;
    margin-right: 5px;
    margin-bottom: 5px;
    background-color: rgba(48, 171, 232, 0.2);
    color: var(--primary-color);
    border: 1px solid rgba(48, 171, 232, 0.3);
}

.tag.success {
    background-color: rgba(85, 209, 135, 0.2);
    color: #55d187;
    border-color: rgba(85, 209, 135, 0.3);
}

.tag.warning {
    background-color: rgba(255, 198, 25, 0.2);
    color: var(--secondary-color);
    border-color: rgba(255, 198, 25, 0.3);
}

.tag.error {
    background-color: rgba(237, 111, 111, 0.2);
    color: #ed6f6f;
    border-color: rgba(237, 111, 111, 0.3);
}

.tag .close {
    margin-left: 5px;
    cursor: pointer;
    font-size: 14px;
    line-height: 1;
}

/* 添加提示框样式 */
.tooltip {
    position: relative;
    display: inline-block;
}

.tooltip .tooltip-text {
    visibility: hidden;
    width: 200px;
    background-color: rgba(5, 32, 46, 0.9);
    color: var(--text-color);
    text-align: center;
    border-radius: 4px;
    padding: 8px;
    position: absolute;
    z-index: 1000;
    bottom: 125%;
    left: 50%;
    transform: translateX(-50%);
    opacity: 0;
    transition: opacity 0.3s;
    font-size: 11px;
    border: 1px solid rgba(48, 171, 232, 0.4);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}

.tooltip .tooltip-text::after {
    content: "";
    position: absolute;
    top: 100%;
    left: 50%;
    margin-left: -5px;
    border-width: 5px;
    border-style: solid;
    border-color: rgba(5, 32, 46, 0.9) transparent transparent transparent;
}

.tooltip:hover .tooltip-text {
    visibility: visible;
    opacity: 1;
}

/* 添加进度条样式 */
.progress-bar {
    width: 100%;
    height: 6px;
    background-color: rgba(5, 32, 46, 0.6);
    border-radius: 3px;
    overflow: hidden;
    margin: 10px 0;
}

.progress-bar .progress {
    height: 100%;
    background-color: var(--primary-color);
    border-radius: 3px;
    transition: width 0.3s ease;
}

.progress-bar .progress.success {
    background-color: #55d187;
}

.progress-bar .progress.warning {
    background-color: var(--secondary-color);
}

.progress-bar .progress.error {
    background-color: #ed6f6f;
}

/* 添加状态指示器 */
.status-indicator {
    display: inline-block;
    width: 10px;
    height: 10px;
    border-radius: 50%;
    margin-right: 5px;
}

.status-indicator.online {
    background-color: #55d187;
    box-shadow: 0 0 5px #55d187;
}

.status-indicator.offline {
    background-color: #8b949e;
}

.status-indicator.warning {
    background-color: var(--secondary-color);
    box-shadow: 0 0 5px var(--secondary-color);
}

.status-indicator.error {
    background-color: #ed6f6f;
    box-shadow: 0 0 5px #ed6f6f;
}

/* 添加卡片组和网格布局 */
.card-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(100px, 1fr));
    gap: 15px;
    margin: 15px 0;
}

.card-group {
    display: flex;
    flex-wrap: wrap;
    gap: 15px;
    margin: 15px 0;
}

/* 添加徽章样式 */
.badge {
    position: relative;
    display: inline-block;
}

.badge[data-count]:after {
    content: attr(data-count);
    position: absolute;
    top: -8px;
    right: -8px;
    min-width: 16px;
    height: 16px;
    line-height: 16px;
    padding: 0 4px;
    font-size: 9px;
    font-weight: bold;
    color: #fff;
    text-align: center;
    background: var(--primary-color);
    border-radius: 10px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
}

.badge.danger[data-count]:after {
    background-color: #ed6f6f;
}

.badge.warning[data-count]:after {
    background-color: var(--secondary-color);
}

.badge.success[data-count]:after {
    background-color: #55d187;
}

/* 固定的选择器/地图容器样式 */
.fixed-selector-container {
    width: 100%;
    height: 600px;
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 8px;
    overflow: hidden;
    position: relative;
    background-color: rgba(30, 30, 30, 0.8);
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.35);
    transition: box-shadow 0.3s ease;
}

.fixed-selector-container:hover {
    box-shadow: 0 6px 18px rgba(0, 0, 0, 0.45);
}

/* 美化默认地图视图 */
.default-map-view {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    background-image: linear-gradient(to bottom, #1a1a1a 0%, #2a2a2a 100%);
}

.map-placeholder {
    text-align: center;
    color: rgba(255, 255, 255, 0.5);
}

.map-placeholder .bi-map {
    font-size: 64px;
    margin-bottom: 15px;
    color: rgba(54, 162, 235, 0.7);
}

.map-placeholder p {
    font-size: 16px;
    margin: 0;
    color: rgba(255, 255, 255, 0.7);
}

.map-view {
    width: 100%;
    height: 100%;
    position: relative;
    background-color: #1a1a1a;
}

#map-canvas {
    width: 100%;
    height: 100%;
    cursor: move;
}

/* 调整嵌入式选择器容器样式 */
.network-selector-embedded {
    width: 100%;
    height: 100%;
    margin: 0;
    padding: 0;
}

.network-selector-embedded .network-selector-modal {
    border-radius: 0;
    box-shadow: none;
}